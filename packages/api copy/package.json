{"name": "@repo/database", "version": "0.0.0", "private": true, "license": "MIT", "scripts": {"dev": "pnpm build --watch", "build": "tsc -b -v", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\""}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["./dist/**"], "publishConfig": {"access": "public"}, "typesVersions": {"*": {"*": ["dist/*"], "server": ["dist/server.d.ts"], "client": ["dist/client.d.ts"]}}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js"}, "./server": {"import": "./dist/server.js", "require": "./dist/server.js"}, "./client": {"import": "./dist/client.js", "require": "./dist/client.js"}, "./*": {"import": "./dist/*.js", "require": "./dist/*.js"}}, "dependencies": {"@nestjs/mapped-types": "*", "drizzle-orm": "^0.44.5", "pg": "^8.16.3", "better-auth": "^1.3.7"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.10.7", "@types/pg": "^8.15.5", "drizzle-kit": "^0.31.4", "ts-loader": "^9.4.3", "ts-node": "^10.9.2", "typescript": "5.5.4"}}